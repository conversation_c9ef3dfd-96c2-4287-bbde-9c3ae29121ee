# 签到查询功能修复部署说明

## 修复完成情况

✅ **已完成的修改**：

1. **数据库更新脚本** - `facai7_api/private/sql/update.sql`
   - 添加了`signin_time`字段的ALTER TABLE语句

2. **签到业务逻辑修改** - `facai7_api/app/index/service/SignInService.php`
   - 修改了`sign()`方法：在个人签到成功后更新user表的signin_time字段
   - 修改了`signTeam()`方法：在团队签到成功后更新user表的signin_time字段

3. **数据迁移脚本** - `temp/signin_time_migration.sql`
   - 为现有用户根据sign_in表设置signin_time字段值

4. **验证脚本** - `temp/signin_verification.sql`
   - 用于验证修复效果和数据一致性

## 部署步骤

### 第一步：备份数据库
```bash
# 备份数据库（请根据实际情况修改参数）
mysqldump -u用户名 -p密码 数据库名 > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 第二步：执行数据库结构更新
```bash
# 执行数据库更新脚本
mysql -u用户名 -p密码 数据库名 < facai7_api/private/sql/update.sql
```

### 第三步：验证字段添加成功
```sql
-- 检查字段是否添加成功
DESCRIBE user;
-- 应该能看到signin_time字段
```

### 第四步：执行数据迁移
```bash
# 执行数据迁移脚本
mysql -u用户名 -p密码 数据库名 < temp/signin_time_migration.sql
```

### 第五步：部署代码更新
```bash
# 部署更新后的SignInService.php文件
# 确保文件权限正确
chmod 644 facai7_api/app/index/service/SignInService.php
```

### 第六步：清理缓存（如果使用了缓存）
```bash
# 清理PHP缓存
php facai7_api/think clear
```

### 第七步：验证修复效果
```bash
# 执行验证脚本
mysql -u用户名 -p密码 数据库名 < temp/signin_verification.sql
```

## 测试验证

### 1. 数据库层面验证
- 执行`temp/signin_verification.sql`中的查询
- 确认signin_time字段存在且数据正确

### 2. 功能测试
- **签到功能测试**：
  - 用户执行签到操作
  - 检查user表signin_time字段是否更新为当前时间戳
  
- **管理后台查询测试**：
  - 登录管理后台
  - 进入用户列表页面
  - 选择"是否签到"选项进行查询
  - 验证不再出现服务器内部错误

### 3. 预期结果
- 选择"是否签到"为"是"：显示今日已签到的用户
- 选择"是否签到"为"否"：显示今日未签到的用户  
- 不选择"是否签到"：显示所有用户

## 回滚方案

如果出现问题，可以执行以下回滚操作：

```sql
-- 1. 删除添加的字段
ALTER TABLE `user` DROP COLUMN `signin_time`;

-- 2. 恢复原始的SignInService.php文件
-- 从版本控制系统恢复或手动删除添加的代码
```

## 监控要点

部署后需要监控：

1. **错误日志**：
   - `facai7_api/runtime/log/` 目录下的错误日志
   - 数据库错误日志

2. **功能验证**：
   - 签到功能是否正常
   - 管理后台查询是否正常
   - 性能是否受影响

3. **数据一致性**：
   - 定期执行验证脚本检查数据一致性

## 注意事项

1. **执行顺序**：严格按照步骤顺序执行，不要跳过任何步骤
2. **备份重要**：执行任何数据库修改前务必备份
3. **测试环境**：建议先在测试环境完整验证
4. **监控日志**：部署后密切监控错误日志
5. **用户影响**：签到功能可能需要短暂停止服务

## 完成标志

修复成功的标志：
- ✅ 数据库中user表包含signin_time字段
- ✅ 现有用户的signin_time字段已正确设置
- ✅ 签到功能正常更新signin_time字段
- ✅ 管理后台"是否签到"查询功能正常工作
- ✅ 无服务器内部错误

## 联系信息

如在部署过程中遇到问题，请及时联系技术支持团队。
