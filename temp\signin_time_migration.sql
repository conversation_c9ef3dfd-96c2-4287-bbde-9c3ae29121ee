-- 用户签到时间字段数据迁移脚本
-- 执行时间：请在添加signin_time字段后执行此脚本

-- 1. 首先检查字段是否存在
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'user' 
AND COLUMN_NAME = 'signin_time';

-- 2. 为现有用户根据sign_in表的最新记录更新signin_time字段
-- 只更新个人签到记录（is_team = 0），不包括团队签到
UPDATE `user` u 
SET signin_time = (
    SELECT MAX(create_time) 
    FROM sign_in s 
    WHERE s.uid = u.id AND s.is_team = 0
) 
WHERE EXISTS (
    SELECT 1 FROM sign_in s 
    WHERE s.uid = u.id AND s.is_team = 0
);

-- 3. 验证数据迁移结果
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN signin_time > 0 THEN 1 END) as users_with_signin_time,
    COUNT(CASE WHEN signin_time = 0 THEN 1 END) as users_without_signin_time
FROM `user`;

-- 4. 查看最近签到的用户示例
SELECT 
    id, 
    username, 
    phone,
    signin_time,
    FROM_UNIXTIME(signin_time) as signin_datetime
FROM `user` 
WHERE signin_time > 0 
ORDER BY signin_time DESC 
LIMIT 10;

-- 5. 验证与sign_in表的数据一致性
SELECT 
    u.id,
    u.username,
    u.signin_time,
    FROM_UNIXTIME(u.signin_time) as user_signin_time,
    s.max_signin_time,
    FROM_UNIXTIME(s.max_signin_time) as sign_in_max_time
FROM `user` u
LEFT JOIN (
    SELECT uid, MAX(create_time) as max_signin_time
    FROM sign_in 
    WHERE is_team = 0
    GROUP BY uid
) s ON u.id = s.uid
WHERE u.signin_time > 0
AND u.signin_time != s.max_signin_time
LIMIT 5;
