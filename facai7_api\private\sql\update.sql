INSERT INTO `facai7`.`system_set` (`key`, `val`, `desc`) VALUES ('withdrawal_interval', '72', '提现间隔（小时）');

ALTER TABLE `payment`
    ADD COLUMN `is_voice` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '是否静音0,正常1静音' AFTER `is_test`;
ALTER TABLE `item_order`
    CHANGE COLUMN `gift_bonus` `gift_bonus` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT '项目补贴' AFTER `amount`;

ALTER TABLE `article`
    ADD COLUMN `release_time` INT NOT NULL DEFAULT 0 COMMENT '发布时间' AFTER `desc`;

ALTER TABLE `group_buy`
DROP COLUMN `process_time`,
DROP COLUMN `process_num`,
DROP COLUMN `process_update_time`;

ALTER TABLE `group_buy`
    ADD COLUMN `people_add` INT(11) NOT NULL DEFAULT '0' COMMENT '每小时增加人数' AFTER `people`;

ALTER TABLE `group_buy`
    ADD COLUMN `people_time` INT(11) NOT NULL DEFAULT '0' COMMENT '人数更新时间' AFTER `people_add`;

ALTER TABLE `withdraw`
    ADD COLUMN `is_energy` TINYINT NOT NULL DEFAULT 0 COMMENT '是否能量0普通1能量' AFTER `remark`;


ALTER TABLE `payment_channel`
    ADD COLUMN `sort` INT NOT NULL DEFAULT 0 COMMENT '排序' AFTER `accounts_name`;

ALTER TABLE `payment_channel`
    ADD COLUMN `desc` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '描述' AFTER `sort`;


ALTER TABLE `item_order`
    ADD COLUMN `profit_earn` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT '每次收益金额（元）' AFTER `profit_now`;


ALTER TABLE `item_order`
    ADD COLUMN `gift_goods` INT NOT NULL DEFAULT 0 COMMENT '商品id' AFTER `update_at`,
	ADD COLUMN `gift_product_expires` INT NOT NULL DEFAULT 0 COMMENT '到期赠送现金' AFTER `gift_goods`;

ALTER TABLE `item`
DROP COLUMN `coupon_limit`;

ALTER TABLE `item`
DROP COLUMN `video_link`;

ALTER TABLE `item`
DROP COLUMN `gift_coupon`;

ALTER TABLE `item`
DROP COLUMN `deduction`;


ALTER TABLE `item`
    ADD COLUMN `home_display` TINYINT NOT NULL DEFAULT '0' COMMENT '首页展示0不展示1展示' AFTER `update_at`;
ALTER TABLE `item`
    ADD COLUMN `product_release_time` INT NOT NULL DEFAULT 0 COMMENT '商品发布时间' AFTER `home_display`;
ALTER TABLE `item`
    ADD COLUMN `product_insurance_time` INT NOT NULL DEFAULT '0' COMMENT '商品投保时间' AFTER `product_release_time`;

ALTER TABLE `item`
    ADD COLUMN `gift_goods` INT(11) NOT NULL DEFAULT '0' COMMENT '赠送实物产品' AFTER `product_insurance_time`;
ALTER TABLE `item`
    ADD COLUMN `level_income` TINYINT NOT NULL DEFAULT 0 COMMENT '等级收益' AFTER `gift_goods`;

ALTER TABLE `item`
    ADD COLUMN `gift_product_expires` INT NOT NULL DEFAULT '0' COMMENT '到期赠送现金' AFTER `level_income`;


ALTER TABLE `raffle`
    ADD COLUMN `type` TINYINT NOT NULL DEFAULT 0 COMMENT '0金额1积分' AFTER `chance`;

ALTER TABLE `raffle_log`
    ADD COLUMN `type` TINYINT(4) NOT NULL DEFAULT '0' COMMENT '类型0金额1积分' AFTER `is_test`;

INSERT INTO `system_set` (`key`, `val`, `desc`) VALUES ('transfer_level', '1', '提现等级');

CREATE TABLE `message` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `uid` int(11) NOT NULL COMMENT '用户id',
    `username` varchar(50) NOT NULL DEFAULT '' COMMENT '用户名称',
    `phone` varchar(50) NOT NULL DEFAULT '' COMMENT '用户手机号码',
    `is_test` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否测试0正常1测试',
    `is_read` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已读0未读1已读',
    `title` VARCHAR(50) NOT NULL DEFAULT '' COLLATE 'utf8mb4_general_ci',
    `desc` TEXT(65535) NOT NULL COLLATE 'utf8mb4_general_ci',
    `create_time` INT(11) NOT NULL DEFAULT '0',
    `update_time` INT(11) NOT NULL DEFAULT '0',
    `create_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`) USING BTREE
)
COMMENT='站内信'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
AUTO_INCREMENT=4
;

ALTER TABLE `user_words_logs` CHANGE COLUMN `words` `words` VARCHAR(50) NOT NULL DEFAULT '0' COMMENT '字' AFTER `is_test`;

CREATE TABLE `user_words` (
   `id` int(11) NOT NULL,
   `uid` int(11) NOT NULL COMMENT '用户id',
   `username` varchar(50) NOT NULL DEFAULT '' COMMENT '用户名称',
   `phone` varchar(50) NOT NULL DEFAULT '' COMMENT '用户手机号码',
   `is_test` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否测试0正常1测试',
   `zhi` INT(11) NOT NULL DEFAULT '0'  COMMENT '智',
   `qi` INT(11) NOT NULL DEFAULT '0'  COMMENT '启',
   `wei` INT(11) NOT NULL DEFAULT '0'  COMMENT '未',
   `lai` INT(11) NOT NULL DEFAULT '0'  COMMENT '来',
   `chuang` INT(11) NOT NULL DEFAULT '0'  COMMENT '创',
   `ling` INT(11) NOT NULL DEFAULT '0'  COMMENT '领',
   `wu` INT(11) NOT NULL DEFAULT '0'  COMMENT '无',
   `xian` INT(11) NOT NULL DEFAULT '0'  COMMENT '限',
   `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
   `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
   `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户集卡' ROW_FORMAT=DYNAMIC;

CREATE TABLE `user_words_logs` (
  `id` int(11) NOT NULL,
  `uid` int(11) NOT NULL COMMENT '用户id',
  `username` varchar(50) NOT NULL DEFAULT '' COMMENT '用户名称',
  `phone` varchar(50) NOT NULL DEFAULT '' COMMENT '用户手机号码',
  `is_test` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否测试0正常1测试',
  `words` INT(11) NOT NULL DEFAULT '0'  COMMENT '字体',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户集卡记录' ROW_FORMAT=DYNAMIC;

CREATE TABLE `shares` (
   `id` int(11) NOT NULL,
   `title` varchar(50) NOT NULL DEFAULT '' COMMENT '股权名称',
   `desc` varchar(50) NOT NULL DEFAULT '' COMMENT '股权描述',
   `img` varchar(50) NOT NULL DEFAULT '' COMMENT '股权图片',
   `shares` INT(11) NOT NULL DEFAULT '0'  COMMENT '股权',
   `cycle` INT(11) NOT NULL DEFAULT '0'  COMMENT '周期',
   `money` INT(11) NOT NULL DEFAULT '0'  COMMENT '每股的钱',
   `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
   `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
   `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='股权表' ROW_FORMAT=DYNAMIC;

CREATE TABLE `shares_logs` (
   `id` int(11) NOT NULL,
   `uid` int(11) NOT NULL COMMENT '用户id',
   `username` varchar(50) NOT NULL DEFAULT '' COMMENT '用户名称',
   `phone` varchar(50) NOT NULL DEFAULT '' COMMENT '用户手机号码',
   `is_test` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否测试0正常1测试',
   `cycle_start` INT(11) NOT NULL DEFAULT '0'  COMMENT '周期开始',
   `cycle_end` INT(11) NOT NULL DEFAULT '0'  COMMENT '周期结束',
   `status` INT(11) NOT NULL DEFAULT '0'  COMMENT '0进行中1已结束',
   `shares` INT(11) NOT NULL DEFAULT '0'  COMMENT '股权',
   `money` INT(11) NOT NULL DEFAULT '0'  COMMENT '每股的钱',
   `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
   `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
   `create_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `update_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='股权表记录' ROW_FORMAT=DYNAMIC;



ALTER TABLE `user`
    ADD COLUMN `is_test` TINYINT NOT NULL DEFAULT 0 COMMENT '0正常1测试 冗余字段' AFTER `pin`

ALTER TABLE `user_info`
    ADD COLUMN `invest_not_finish` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT '用户在订购中金额' AFTER `invest_money`;

ALTER TABLE `item_order` ADD COLUMN `principal` DECIMAL(10,2) NOT NULL DEFAULT '0' COMMENT '本金金额' AFTER `update_at`;

ALTER TABLE `item`
    ADD COLUMN `company_name` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '公司名称' AFTER `deduction`;

ALTER TABLE `item_order`
    ADD COLUMN `company_name` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '公司名称' AFTER `order_no`;



ALTER TABLE `payment_account`
    ADD COLUMN `bank_owner` VARCHAR(60) NOT NULL DEFAULT '' COMMENT '银行归属人' AFTER `bank_name`;



ALTER TABLE `question`
    CHANGE COLUMN `desc` `desc` TEXT NOT NULL COLLATE 'utf8mb4_general_ci' AFTER `title`;


INSERT INTO `facai4`.`money_class` (`title`) VALUES ('首存奖励');
INSERT INTO `facai4`.`system_set` (`key`, `val`, `class`, `desc`) VALUES ('first_deposit_bonus', '100', 'basic', '首存奖励');

ALTER TABLE `item`
    CHANGE COLUMN `coupon_limit` `coupon_limit` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT '执行卡限制' AFTER `update_at`;



CREATE TABLE `question` (
`id` INT(11) NOT NULL AUTO_INCREMENT,
`title` VARCHAR(50) NOT NULL DEFAULT '' COLLATE 'utf8mb4_general_ci',
`desc` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_general_ci',
`create_time` INT(11) NOT NULL DEFAULT '0',
`update_time` INT(11) NOT NULL DEFAULT '0',
`create_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
`update_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='问题列表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
AUTO_INCREMENT=4
;

#
ALTER TABLE `item` ADD COLUMN `coupon_limit` DECIMAL(5,2) NOT NULL DEFAULT '0' COMMENT '执行卡限制' AFTER `update_at`;

-- 修复用户列表签到查询功能：添加signin_time字段
ALTER TABLE `user`
ADD COLUMN `signin_time` INT(11) NOT NULL DEFAULT '0' COMMENT '最后签到时间' AFTER `login_time`;