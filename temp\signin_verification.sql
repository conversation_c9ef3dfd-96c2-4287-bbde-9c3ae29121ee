-- 签到功能修复验证脚本

-- 1. 验证signin_time字段是否存在
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'user' 
AND COLUMN_NAME = 'signin_time';

-- 2. 检查今日签到用户数据
SELECT 
    COUNT(*) as today_signin_users
FROM `user` 
WHERE signin_time >= UNIX_TIMESTAMP(CURDATE());

-- 3. 模拟管理后台查询：查找今日已签到的用户
SELECT 
    u.id,
    u.username,
    u.phone,
    u.signin_time,
    FROM_UNIXTIME(u.signin_time) as signin_datetime
FROM `user` u
WHERE u.signin_time >= UNIX_TIMESTAMP(CURDATE())
ORDER BY u.signin_time DESC
LIMIT 10;

-- 4. 模拟管理后台查询：查找今日未签到的用户
SELECT 
    u.id,
    u.username,
    u.phone,
    u.signin_time,
    CASE 
        WHEN u.signin_time = 0 THEN '从未签到'
        ELSE FROM_UNIXTIME(u.signin_time)
    END as last_signin_time
FROM `user` u
WHERE u.signin_time < UNIX_TIMESTAMP(CURDATE()) OR u.signin_time = 0
ORDER BY u.id
LIMIT 10;

-- 5. 验证签到记录与用户表的一致性
SELECT
    '今日sign_in表记录数(个人签到)' as description,
    COUNT(*) as count
FROM sign_in
WHERE create_time >= UNIX_TIMESTAMP(CURDATE())
AND is_team = 0

UNION ALL

SELECT
    '今日sign_in表记录数(团队签到)' as description,
    COUNT(*) as count
FROM sign_in
WHERE create_time >= UNIX_TIMESTAMP(CURDATE())
AND is_team = 1

UNION ALL

SELECT
    '今日sign_in表记录数(总计)' as description,
    COUNT(*) as count
FROM sign_in
WHERE create_time >= UNIX_TIMESTAMP(CURDATE())

UNION ALL

SELECT
    '今日user表signin_time更新数' as description,
    COUNT(*) as count
FROM `user`
WHERE signin_time >= UNIX_TIMESTAMP(CURDATE());

-- 6. 检查是否有数据不一致的情况
SELECT
    'sign_in有记录但user表signin_time未更新' as issue_type,
    COUNT(*) as count
FROM (
    SELECT DISTINCT s.uid
    FROM sign_in s
    LEFT JOIN `user` u ON s.uid = u.id
    WHERE s.create_time >= UNIX_TIMESTAMP(CURDATE())
    AND (u.signin_time < UNIX_TIMESTAMP(CURDATE()) OR u.signin_time IS NULL)
) as inconsistent_data

UNION ALL

SELECT
    'user表有今日signin_time但sign_in无记录' as issue_type,
    COUNT(*) as count
FROM (
    SELECT u.id
    FROM `user` u
    LEFT JOIN sign_in s ON u.id = s.uid
        AND s.create_time >= UNIX_TIMESTAMP(CURDATE())
    WHERE u.signin_time >= UNIX_TIMESTAMP(CURDATE())
    AND s.uid IS NULL
) as inconsistent_data;
