-- 查询逻辑修复验证脚本
-- 用于验证修复后的查询逻辑是否正确

SELECT '=== 查询逻辑修复验证开始 ===' as verification_info;

-- 1. 检查当前时间和今日时间戳
SELECT '1. 时间基准检查' as check_item;
SELECT 
    NOW() as current_datetime,
    CURDATE() as today_date,
    UNIX_TIMESTAMP(CURDATE()) as today_start_timestamp,
    FROM_UNIXTIME(UNIX_TIMESTAMP(CURDATE())) as today_start_datetime;

-- 2. 检查测试用户的signin_time状态
SELECT '2. 测试用户signin_time状态' as check_item;
SELECT 
    username,
    signin_time,
    FROM_UNIXTIME(signin_time) as signin_datetime,
    CASE 
        WHEN signin_time >= UNIX_TIMESTAMP(CURDATE()) THEN '今日已签到'
        WHEN signin_time > 0 THEN '历史签到'
        ELSE '从未签到'
    END as signin_status,
    CASE 
        WHEN signin_time >= UNIX_TIMESTAMP(CURDATE()) THEN 'is_signin=1应该显示'
        ELSE 'is_signin=0应该显示'
    END as expected_query_result
FROM `user` 
WHERE username LIKE 'test_user_%'
ORDER BY username;

-- 3. 模拟修复后的查询逻辑

-- 3.1 模拟 is_signin=1 的查询（今日已签到用户）
SELECT '3.1 模拟 is_signin=1 查询（今日已签到用户）' as query_simulation;
SELECT 
    ur.id,
    ur.username,
    ur.phone,
    ur.signin_time,
    FROM_UNIXTIME(ur.signin_time) as signin_datetime,
    '✅ 应该在is_signin=1结果中显示' as note
FROM `user` ur
JOIN `user_info` fo ON ur.id = fo.uid
JOIN `user_state` st ON ur.id = st.uid
JOIN `money` mo ON ur.id = mo.uid
WHERE ur.signin_time >= UNIX_TIMESTAMP(CURDATE())
AND ur.username LIKE 'test_user_%'
ORDER BY ur.signin_time DESC;

-- 3.2 模拟 is_signin=0 的查询（今日未签到用户）
SELECT '3.2 模拟 is_signin=0 查询（今日未签到用户）' as query_simulation;
SELECT 
    ur.id,
    ur.username,
    ur.phone,
    ur.signin_time,
    CASE 
        WHEN ur.signin_time = 0 THEN '从未签到'
        ELSE FROM_UNIXTIME(ur.signin_time)
    END as last_signin_time,
    '✅ 应该在is_signin=0结果中显示' as note
FROM `user` ur
JOIN `user_info` fo ON ur.id = fo.uid
JOIN `user_state` st ON ur.id = st.uid
JOIN `money` mo ON ur.id = mo.uid
WHERE ur.signin_time < UNIX_TIMESTAMP(CURDATE())
AND ur.username LIKE 'test_user_%'
ORDER BY ur.signin_time DESC;

-- 4. 验证查询结果的完整性
SELECT '4. 查询结果完整性验证' as check_item;

-- 统计各类用户数量
SELECT 
    '今日已签到用户数(is_signin=1)' as user_type,
    COUNT(*) as count,
    GROUP_CONCAT(username) as usernames
FROM `user` ur
JOIN `user_info` fo ON ur.id = fo.uid
JOIN `user_state` st ON ur.id = st.uid
JOIN `money` mo ON ur.id = mo.uid
WHERE ur.signin_time >= UNIX_TIMESTAMP(CURDATE())
AND ur.username LIKE 'test_user_%'

UNION ALL

SELECT 
    '今日未签到用户数(is_signin=0)' as user_type,
    COUNT(*) as count,
    GROUP_CONCAT(username) as usernames
FROM `user` ur
JOIN `user_info` fo ON ur.id = fo.uid
JOIN `user_state` st ON ur.id = st.uid
JOIN `money` mo ON ur.id = mo.uid
WHERE ur.signin_time < UNIX_TIMESTAMP(CURDATE())
AND ur.username LIKE 'test_user_%'

UNION ALL

SELECT 
    '总测试用户数' as user_type,
    COUNT(*) as count,
    GROUP_CONCAT(username) as usernames
FROM `user` ur
JOIN `user_info` fo ON ur.id = fo.uid
JOIN `user_state` st ON ur.id = st.uid
JOIN `money` mo ON ur.id = mo.uid
WHERE ur.username LIKE 'test_user_%';

-- 5. 检查是否存在数据重叠或遗漏
SELECT '5. 数据完整性检查' as check_item;

-- 验证两个查询结果的总和是否等于总用户数
SELECT 
    (SELECT COUNT(*) FROM `user` ur
     JOIN `user_info` fo ON ur.id = fo.uid
     JOIN `user_state` st ON ur.id = st.uid
     JOIN `money` mo ON ur.id = mo.uid
     WHERE ur.signin_time >= UNIX_TIMESTAMP(CURDATE())
     AND ur.username LIKE 'test_user_%') as signed_users,
     
    (SELECT COUNT(*) FROM `user` ur
     JOIN `user_info` fo ON ur.id = fo.uid
     JOIN `user_state` st ON ur.id = st.uid
     JOIN `money` mo ON ur.id = mo.uid
     WHERE ur.signin_time < UNIX_TIMESTAMP(CURDATE())
     AND ur.username LIKE 'test_user_%') as unsigned_users,
     
    (SELECT COUNT(*) FROM `user` ur
     JOIN `user_info` fo ON ur.id = fo.uid
     JOIN `user_state` st ON ur.id = st.uid
     JOIN `money` mo ON ur.id = mo.uid
     WHERE ur.username LIKE 'test_user_%') as total_users,
     
    CASE 
        WHEN (
            (SELECT COUNT(*) FROM `user` ur
             JOIN `user_info` fo ON ur.id = fo.uid
             JOIN `user_state` st ON ur.id = st.uid
             JOIN `money` mo ON ur.id = mo.uid
             WHERE ur.signin_time >= UNIX_TIMESTAMP(CURDATE())
             AND ur.username LIKE 'test_user_%') +
            (SELECT COUNT(*) FROM `user` ur
             JOIN `user_info` fo ON ur.id = fo.uid
             JOIN `user_state` st ON ur.id = st.uid
             JOIN `money` mo ON ur.id = mo.uid
             WHERE ur.signin_time < UNIX_TIMESTAMP(CURDATE())
             AND ur.username LIKE 'test_user_%')
        ) = (SELECT COUNT(*) FROM `user` ur
             JOIN `user_info` fo ON ur.id = fo.uid
             JOIN `user_state` st ON ur.id = st.uid
             JOIN `money` mo ON ur.id = mo.uid
             WHERE ur.username LIKE 'test_user_%')
        THEN '✅ 数据完整，无重叠无遗漏'
        ELSE '❌ 数据不完整，存在问题'
    END as data_integrity_check;

-- 6. 预期的管理后台测试结果
SELECT '6. 预期的管理后台测试结果' as expected_results;

SELECT 
    username,
    CASE 
        WHEN signin_time >= UNIX_TIMESTAMP(CURDATE()) THEN 
            CONCAT('✅ 选择"是否签到"="是"时应该显示 (', FROM_UNIXTIME(signin_time), ')')
        ELSE 
            CONCAT('✅ 选择"是否签到"="否"时应该显示 (', 
                   CASE WHEN signin_time = 0 THEN '从未签到' ELSE FROM_UNIXTIME(signin_time) END, ')')
    END as expected_behavior
FROM `user` 
WHERE username LIKE 'test_user_%'
ORDER BY username;

-- 7. 修复验证总结
SELECT '=== 修复验证总结 ===' as summary;

SELECT 
    CASE 
        WHEN (
            -- 检查是否有今日已签到用户
            (SELECT COUNT(*) FROM `user` ur
             JOIN `user_info` fo ON ur.id = fo.uid
             JOIN `user_state` st ON ur.id = st.uid
             JOIN `money` mo ON ur.id = mo.uid
             WHERE ur.signin_time >= UNIX_TIMESTAMP(CURDATE())
             AND ur.username LIKE 'test_user_%') > 0
            AND
            -- 检查是否有今日未签到用户
            (SELECT COUNT(*) FROM `user` ur
             JOIN `user_info` fo ON ur.id = fo.uid
             JOIN `user_state` st ON ur.id = st.uid
             JOIN `money` mo ON ur.id = mo.uid
             WHERE ur.signin_time < UNIX_TIMESTAMP(CURDATE())
             AND ur.username LIKE 'test_user_%') > 0
        ) THEN '🎉 修复验证通过！可以进行管理后台测试'
        ELSE '⚠️ 测试数据可能不完整，建议重新创建测试数据'
    END as verification_result;

-- 8. 下一步测试指导
SELECT '=== 下一步测试指导 ===' as next_steps;
SELECT 
    '1. 部署修复后的UserService.php文件' as step_1
UNION ALL SELECT 
    '2. 清理缓存（如果有）' as step_2
UNION ALL SELECT 
    '3. 登录管理后台测试"是否签到"查询功能' as step_3
UNION ALL SELECT 
    '4. 验证选择"是"和"否"都能正确显示对应用户' as step_4;
