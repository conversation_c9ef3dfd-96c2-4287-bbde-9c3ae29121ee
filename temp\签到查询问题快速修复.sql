-- 签到查询问题快速修复脚本
-- 根据诊断结果执行相应的修复操作

SELECT '=== 签到查询问题快速修复开始 ===' as fix_info;

-- 修复1: 确保测试用户有完整的关联数据
SELECT '修复1: 创建缺失的关联数据' as fix_step;

-- 为缺少user_info的用户创建记录
INSERT IGNORE INTO `user_info` (
    uid, recharge_money, recharge_num, withdraw_money, withdraw_num,
    signin_money, signin_num, user_points, yuebao_money, invest_money,
    create_time, update_time, create_at, update_at
)
SELECT 
    u.id as uid, 0.00, 0, 0.00, 0,
    0.00, 0, 0, 0.00, 0.00,
    UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), NOW(), NOW()
FROM `user` u
LEFT JOIN `user_info` ui ON u.id = ui.uid
WHERE ui.uid IS NULL;

-- 为缺少user_state的用户创建记录
INSERT IGNORE INTO `user_state` (
    uid, ban_buy, ban_sigin, ban_raffle, ban_login, ban_invite,
    ban_recharge, ban_withdraw, ban_exchange, sfz_status, is_test,
    create_time, update_time, create_at, update_at
)
SELECT 
    u.id as uid, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0,
    UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), NOW(), NOW()
FROM `user` u
LEFT JOIN `user_state` us ON u.id = us.uid
WHERE us.uid IS NULL;

-- 为缺少money的用户创建记录
INSERT IGNORE INTO `money` (
    uid, money, frozen_money, create_time, update_time, create_at, update_at
)
SELECT 
    u.id as uid, 100.00, 0.00,
    UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), NOW(), NOW()
FROM `user` u
LEFT JOIN `money` m ON u.id = m.uid
WHERE m.uid IS NULL;

-- 修复2: 创建今日签到测试数据
SELECT '修复2: 创建今日签到测试数据' as fix_step;

-- 删除可能存在的旧测试数据
DELETE FROM `sign_in` WHERE username LIKE 'test_user_%';

-- 为test_user_1创建今日个人签到记录
INSERT INTO `sign_in` (
    uid, username, phone, amount, is_team, is_test,
    create_time, update_time, create_at, update_at
) VALUES (
    (SELECT id FROM `user` WHERE username = 'test_user_1' LIMIT 1),
    'test_user_1', '13800000001', 1.00, 0, 0,
    UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), NOW(), NOW()
);

-- 为test_user_2创建今日团队签到记录
INSERT INTO `sign_in` (
    uid, username, phone, amount, is_team, is_test,
    create_time, update_time, create_at, update_at
) VALUES (
    (SELECT id FROM `user` WHERE username = 'test_user_2' LIMIT 1),
    'test_user_2', '13800000002', 2.00, 1, 0,
    UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), NOW(), NOW()
);

-- 为test_user_5创建今日个人签到记录（1小时前）
INSERT INTO `sign_in` (
    uid, username, phone, amount, is_team, is_test,
    create_time, update_time, create_at, update_at
) VALUES (
    (SELECT id FROM `user` WHERE username = 'test_user_5' LIMIT 1),
    'test_user_5', '13800000005', 1.00, 0, 0,
    UNIX_TIMESTAMP() - 3600, UNIX_TIMESTAMP() - 3600, 
    DATE_SUB(NOW(), INTERVAL 1 HOUR), DATE_SUB(NOW(), INTERVAL 1 HOUR)
);

-- 为test_user_5创建今日团队签到记录（更晚的时间）
INSERT INTO `sign_in` (
    uid, username, phone, amount, is_team, is_test,
    create_time, update_time, create_at, update_at
) VALUES (
    (SELECT id FROM `user` WHERE username = 'test_user_5' LIMIT 1),
    'test_user_5', '13800000005', 2.00, 1, 0,
    UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), NOW(), NOW()
);

-- 为test_user_4创建昨日签到记录（历史记录）
INSERT INTO `sign_in` (
    uid, username, phone, amount, is_team, is_test,
    create_time, update_time, create_at, update_at
) VALUES (
    (SELECT id FROM `user` WHERE username = 'test_user_4' LIMIT 1),
    'test_user_4', '13800000004', 1.00, 0, 0,
    UNIX_TIMESTAMP() - 86400, UNIX_TIMESTAMP() - 86400, 
    DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)
);

-- 修复3: 更新用户表的signin_time字段
SELECT '修复3: 更新signin_time字段' as fix_step;

-- 为有签到记录的用户更新signin_time
UPDATE `user` u 
SET signin_time = (
    SELECT MAX(create_time) 
    FROM sign_in s 
    WHERE s.uid = u.id
) 
WHERE EXISTS (
    SELECT 1 FROM sign_in s 
    WHERE s.uid = u.id
);

-- 修复4: 验证修复结果
SELECT '修复4: 验证修复结果' as fix_step;

-- 检查今日已签到用户
SELECT 
    '今日已签到用户' as user_type,
    ur.id,
    ur.username,
    ur.signin_time,
    FROM_UNIXTIME(ur.signin_time) as signin_datetime
FROM `user` ur
JOIN `user_info` fo ON ur.id = fo.uid
JOIN `user_state` st ON ur.id = st.uid
JOIN `money` mo ON ur.id = mo.uid
WHERE ur.signin_time >= UNIX_TIMESTAMP(CURDATE())
AND ur.username LIKE 'test_user_%'
ORDER BY ur.signin_time DESC;

-- 检查今日未签到用户
SELECT 
    '今日未签到用户' as user_type,
    ur.id,
    ur.username,
    ur.signin_time,
    CASE 
        WHEN ur.signin_time = 0 THEN '从未签到'
        ELSE FROM_UNIXTIME(ur.signin_time)
    END as last_signin_time
FROM `user` ur
JOIN `user_info` fo ON ur.id = fo.uid
JOIN `user_state` st ON ur.id = st.uid
JOIN `money` mo ON ur.id = mo.uid
WHERE (ur.signin_time < UNIX_TIMESTAMP(CURDATE()) OR ur.signin_time = 0)
AND ur.username LIKE 'test_user_%'
ORDER BY ur.signin_time DESC;

-- 修复5: 为现有真实用户创建关联数据（如果需要）
SELECT '修复5: 检查现有用户数据完整性' as fix_step;

-- 统计缺少关联数据的用户数量
SELECT 
    '缺少user_info的用户数' as missing_type,
    COUNT(*) as count
FROM `user` u
LEFT JOIN `user_info` ui ON u.id = ui.uid
WHERE ui.uid IS NULL

UNION ALL

SELECT 
    '缺少user_state的用户数' as missing_type,
    COUNT(*) as count
FROM `user` u
LEFT JOIN `user_state` us ON u.id = us.uid
WHERE us.uid IS NULL

UNION ALL

SELECT 
    '缺少money的用户数' as missing_type,
    COUNT(*) as count
FROM `user` u
LEFT JOIN `money` m ON u.id = m.uid
WHERE m.uid IS NULL;

-- 如果有缺少关联数据的真实用户，为他们创建数据
-- 注意：这里只为前10个缺少数据的用户创建，避免大量数据操作

-- 为缺少user_info的真实用户创建记录（限制10个）
INSERT IGNORE INTO `user_info` (
    uid, recharge_money, recharge_num, withdraw_money, withdraw_num,
    signin_money, signin_num, user_points, yuebao_money, invest_money,
    create_time, update_time, create_at, update_at
)
SELECT 
    u.id as uid, 0.00, 0, 0.00, 0,
    0.00, 0, 0, 0.00, 0.00,
    UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), NOW(), NOW()
FROM `user` u
LEFT JOIN `user_info` ui ON u.id = ui.uid
WHERE ui.uid IS NULL
AND u.username NOT LIKE 'test_user_%'
LIMIT 10;

-- 为缺少user_state的真实用户创建记录（限制10个）
INSERT IGNORE INTO `user_state` (
    uid, ban_buy, ban_sigin, ban_raffle, ban_login, ban_invite,
    ban_recharge, ban_withdraw, ban_exchange, sfz_status, is_test,
    create_time, update_time, create_at, update_at
)
SELECT 
    u.id as uid, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0,
    UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), NOW(), NOW()
FROM `user` u
LEFT JOIN `user_state` us ON u.id = us.uid
WHERE us.uid IS NULL
AND u.username NOT LIKE 'test_user_%'
LIMIT 10;

-- 为缺少money的真实用户创建记录（限制10个）
INSERT IGNORE INTO `money` (
    uid, money, frozen_money, create_time, update_time, create_at, update_at
)
SELECT 
    u.id as uid, 0.00, 0.00,
    UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), NOW(), NOW()
FROM `user` u
LEFT JOIN `money` m ON u.id = m.uid
WHERE m.uid IS NULL
AND u.username NOT LIKE 'test_user_%'
LIMIT 10;

-- 最终验证
SELECT '=== 修复完成，最终验证 ===' as final_check;

SELECT 
    '修复后统计' as summary_type,
    '今日已签到用户数' as metric,
    COUNT(*) as count
FROM `user` ur
JOIN `user_info` fo ON ur.id = fo.uid
JOIN `user_state` st ON ur.id = st.uid
JOIN `money` mo ON ur.id = mo.uid
WHERE ur.signin_time >= UNIX_TIMESTAMP(CURDATE())

UNION ALL

SELECT 
    '修复后统计' as summary_type,
    '今日未签到用户数' as metric,
    COUNT(*) as count
FROM `user` ur
JOIN `user_info` fo ON ur.id = fo.uid
JOIN `user_state` st ON ur.id = st.uid
JOIN `money` mo ON ur.id = mo.uid
WHERE ur.signin_time < UNIX_TIMESTAMP(CURDATE()) OR ur.signin_time = 0

UNION ALL

SELECT 
    '修复后统计' as summary_type,
    '总用户数' as metric,
    COUNT(*) as count
FROM `user` ur
JOIN `user_info` fo ON ur.id = fo.uid
JOIN `user_state` st ON ur.id = st.uid
JOIN `money` mo ON ur.id = mo.uid;

SELECT '✅ 修复完成！现在可以测试管理后台的签到查询功能了。' as completion_message;
