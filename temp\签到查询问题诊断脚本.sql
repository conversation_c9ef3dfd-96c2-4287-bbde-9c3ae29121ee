-- 签到查询问题诊断脚本
-- 用于快速定位"是否签到"查询无结果的问题

SELECT '=== 签到查询问题诊断开始 ===' as diagnosis_info;

-- 1. 检查signin_time字段是否存在
SELECT '1. 检查signin_time字段是否存在' as check_item;
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ signin_time字段存在'
        ELSE '❌ signin_time字段不存在 - 请先执行数据库更新脚本'
    END as result
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'user' 
AND COLUMN_NAME = 'signin_time';

-- 2. 检查当前时间和今日时间戳
SELECT '2. 检查时间设置' as check_item;
SELECT 
    NOW() as current_datetime,
    UNIX_TIMESTAMP() as current_timestamp,
    CURDATE() as today_date,
    UNIX_TIMESTAMP(CURDATE()) as today_start_timestamp,
    UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 1 DAY)) as tomorrow_start_timestamp,
    FROM_UNIXTIME(UNIX_TIMESTAMP(CURDATE())) as today_start_datetime;

-- 3. 检查所有用户的signin_time状态
SELECT '3. 检查用户signin_time状态' as check_item;
SELECT 
    id,
    username,
    phone,
    signin_time,
    CASE 
        WHEN signin_time = 0 THEN '从未签到'
        WHEN signin_time >= UNIX_TIMESTAMP(CURDATE()) THEN '今日已签到'
        ELSE CONCAT('历史签到: ', FROM_UNIXTIME(signin_time))
    END as signin_status,
    FROM_UNIXTIME(signin_time) as signin_datetime
FROM `user` 
WHERE username LIKE 'test_user_%' OR id <= 10
ORDER BY signin_time DESC;

-- 4. 检查sign_in表的记录
SELECT '4. 检查sign_in表记录' as check_item;
SELECT 
    s.id,
    s.uid,
    u.username,
    s.amount,
    CASE WHEN s.is_team = 0 THEN '个人签到' ELSE '团队签到' END as signin_type,
    s.create_time,
    FROM_UNIXTIME(s.create_time) as signin_datetime,
    CASE 
        WHEN s.create_time >= UNIX_TIMESTAMP(CURDATE()) THEN '今日签到'
        ELSE '历史签到'
    END as time_status
FROM sign_in s
LEFT JOIN `user` u ON s.uid = u.id
WHERE u.username LIKE 'test_user_%' OR s.create_time >= UNIX_TIMESTAMP(CURDATE()) - 86400
ORDER BY s.create_time DESC
LIMIT 20;

-- 5. 模拟管理后台查询逻辑
SELECT '5. 模拟管理后台查询逻辑' as check_item;

-- 查询今日已签到用户（is_signin = 1）
SELECT '今日已签到用户查询结果:' as query_type;
SELECT 
    ur.id,
    ur.username,
    ur.phone,
    ur.signin_time,
    FROM_UNIXTIME(ur.signin_time) as signin_datetime
FROM `user` ur
JOIN `user_info` fo ON ur.id = fo.uid
JOIN `user_state` st ON ur.id = st.uid
JOIN `money` mo ON ur.id = mo.uid
WHERE ur.signin_time >= UNIX_TIMESTAMP(CURDATE())
ORDER BY ur.signin_time DESC
LIMIT 10;

-- 查询今日未签到用户（is_signin = 0）
SELECT '今日未签到用户查询结果:' as query_type;
SELECT 
    ur.id,
    ur.username,
    ur.phone,
    ur.signin_time,
    CASE 
        WHEN ur.signin_time = 0 THEN '从未签到'
        ELSE FROM_UNIXTIME(ur.signin_time)
    END as last_signin_time
FROM `user` ur
JOIN `user_info` fo ON ur.id = fo.uid
JOIN `user_state` st ON ur.id = st.uid
JOIN `money` mo ON ur.id = mo.uid
WHERE ur.signin_time < UNIX_TIMESTAMP(CURDATE()) OR ur.signin_time = 0
ORDER BY ur.signin_time DESC
LIMIT 10;

-- 6. 检查关联表是否存在问题
SELECT '6. 检查关联表数据' as check_item;
SELECT 
    '用户表记录数' as table_name,
    COUNT(*) as record_count
FROM `user`
UNION ALL
SELECT 
    'user_info表记录数' as table_name,
    COUNT(*) as record_count
FROM `user_info`
UNION ALL
SELECT 
    'user_state表记录数' as table_name,
    COUNT(*) as record_count
FROM `user_state`
UNION ALL
SELECT 
    'money表记录数' as table_name,
    COUNT(*) as record_count
FROM `money`;

-- 7. 检查是否有用户缺少关联数据
SELECT '7. 检查用户关联数据完整性' as check_item;
SELECT 
    u.id,
    u.username,
    CASE WHEN ui.uid IS NULL THEN '❌ 缺少user_info' ELSE '✅ 有user_info' END as user_info_status,
    CASE WHEN us.uid IS NULL THEN '❌ 缺少user_state' ELSE '✅ 有user_state' END as user_state_status,
    CASE WHEN m.uid IS NULL THEN '❌ 缺少money' ELSE '✅ 有money' END as money_status
FROM `user` u
LEFT JOIN `user_info` ui ON u.id = ui.uid
LEFT JOIN `user_state` us ON u.id = us.uid
LEFT JOIN `money` m ON u.id = m.uid
WHERE u.username LIKE 'test_user_%'
ORDER BY u.id;

-- 8. 检查数据迁移是否正确执行
SELECT '8. 检查数据迁移结果' as check_item;
SELECT 
    u.username,
    u.signin_time as user_signin_time,
    s.max_signin_time as sign_in_max_time,
    CASE 
        WHEN u.signin_time = s.max_signin_time THEN '✅ 数据一致'
        WHEN s.max_signin_time IS NULL AND u.signin_time = 0 THEN '✅ 无签到记录，数据正确'
        WHEN s.max_signin_time IS NOT NULL AND u.signin_time = 0 THEN '❌ 有签到记录但signin_time未更新'
        ELSE '❌ 数据不一致'
    END as migration_status,
    FROM_UNIXTIME(u.signin_time) as user_signin_datetime,
    FROM_UNIXTIME(s.max_signin_time) as sign_in_max_datetime
FROM `user` u
LEFT JOIN (
    SELECT uid, MAX(create_time) as max_signin_time
    FROM sign_in 
    GROUP BY uid
) s ON u.id = s.uid
WHERE u.username LIKE 'test_user_%'
ORDER BY u.username;

-- 9. 问题诊断总结
SELECT '=== 问题诊断总结 ===' as diagnosis_summary;

-- 统计各种情况的用户数量
SELECT 
    '今日已签到用户数' as status_type,
    COUNT(*) as user_count
FROM `user` ur
JOIN `user_info` fo ON ur.id = fo.uid
JOIN `user_state` st ON ur.id = st.uid
JOIN `money` mo ON ur.id = mo.uid
WHERE ur.signin_time >= UNIX_TIMESTAMP(CURDATE())

UNION ALL

SELECT 
    '今日未签到用户数' as status_type,
    COUNT(*) as user_count
FROM `user` ur
JOIN `user_info` fo ON ur.id = fo.uid
JOIN `user_state` st ON ur.id = st.uid
JOIN `money` mo ON ur.id = mo.uid
WHERE ur.signin_time < UNIX_TIMESTAMP(CURDATE()) OR ur.signin_time = 0

UNION ALL

SELECT 
    '总用户数' as status_type,
    COUNT(*) as user_count
FROM `user` ur
JOIN `user_info` fo ON ur.id = fo.uid
JOIN `user_state` st ON ur.id = st.uid
JOIN `money` mo ON ur.id = mo.uid;

-- 10. 可能的问题和解决方案
SELECT '=== 可能的问题和解决方案 ===' as solutions;
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'user' AND COLUMN_NAME = 'signin_time') = 0 
        THEN '问题: signin_time字段不存在\n解决方案: 执行数据库更新脚本添加字段'
        
        WHEN (SELECT COUNT(*) FROM `user` WHERE signin_time > 0) = 0 
        THEN '问题: 所有用户signin_time都为0\n解决方案: 执行数据迁移脚本或手动更新测试数据'
        
        WHEN (SELECT COUNT(*) FROM `user` ur JOIN `user_info` fo ON ur.id = fo.uid JOIN `user_state` st ON ur.id = st.uid JOIN `money` mo ON ur.id = mo.uid WHERE ur.signin_time >= UNIX_TIMESTAMP(CURDATE())) = 0
        THEN '问题: 没有今日签到用户\n解决方案: 创建今日签到测试数据'
        
        WHEN (SELECT COUNT(*) FROM `user` u LEFT JOIN `user_info` ui ON u.id = ui.uid WHERE ui.uid IS NULL) > 0
        THEN '问题: 部分用户缺少关联表数据\n解决方案: 为用户创建完整的关联数据'
        
        ELSE '数据看起来正常，可能是前端或查询参数问题'
    END as diagnosis_result;
